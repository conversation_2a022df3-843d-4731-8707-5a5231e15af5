import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { Session } from 'next-auth';
import { authOptions } from '@/lib/auth';
import connectDB from '@/lib/db';
import Contact from '@/models/Contact';

interface ContactData {
  contact_companyName: string;
  contact_contactPerson: string;
  contact_email: string;
  contact_phone: string;
  contact_category: string;
  contact_address: string;
  contact_notes?: string;
}

// GET endpoint do pobierania kontaktów
export async function GET(request: NextRequest) {
  try {
    const session = (await getServerSession(authOptions)) as Session | null;

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const category = searchParams.get('category');
    const search = searchParams.get('search');

    await connectDB();

    // Buduj filtr - pokaż kontakty publiczne (userId = "0") oraz kontakty użytkownika
    const filter: {
      $or?: Array<{ userId: string }>;
      $and?: Array<Record<string, unknown>>;
      contact_category?: { $regex: RegExp };
    } = {
      $or: [
        { userId: "0" }, // Kontakty publiczne
        { userId: session.user.id } // Kontakty użytkownika
      ]
    };

    if (category) {
      filter.contact_category = { $regex: new RegExp(`^${category}$`, 'i') };
    }

    if (search) {
      filter.$and = [
        filter.$or ? { $or: filter.$or } : {},
        {
          $or: [
            { contact_companyName: { $regex: search, $options: 'i' } },
            { contact_contactPerson: { $regex: search, $options: 'i' } },
            { contact_email: { $regex: search, $options: 'i' } },
            { contact_phone: { $regex: search, $options: 'i' } }
          ]
        }
      ];
      delete filter.$or; // Usuń $or z głównego filtra, bo teraz używamy $and
    }

    // Pobierz kontakty z paginacją
    const contacts = await Contact.find(filter)
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(limit);

    // Policz całkowitą liczbę kontaktów
    const totalCount = await Contact.countDocuments(filter);

    // Pobierz unikalne kategorie (publiczne + użytkownika)
    const categories = await Contact.distinct('contact_category', {
      $or: [
        { userId: "0" }, // Kontakty publiczne
        { userId: session.user.id } // Kontakty użytkownika
      ]
    });

    return NextResponse.json({
      success: true,
      contacts: contacts.map(contact => ({
        id: contact._id,
        contact_companyName: contact.contact_companyName,
        contact_contactPerson: contact.contact_contactPerson,
        contact_email: contact.contact_email,
        contact_phone: contact.contact_phone,
        contact_category: contact.contact_category,
        contact_address: contact.contact_address,
        contact_notes: contact.contact_notes,
        createdAt: contact.createdAt,
        updatedAt: contact.updatedAt
      })),
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasNext: page * limit < totalCount,
        hasPrev: page > 1
      },
      categories
    });

  } catch (error) {
    console.error('Błąd pobierania kontaktów:', error);
    return NextResponse.json(
      { error: 'Wystąpił błąd podczas pobierania kontaktów' },
      { status: 500 }
    );
  }
}

// POST endpoint do dodawania nowego kontaktu (publiczny - bez autoryzacji)
export async function POST(request: NextRequest) {
  try {
    const contactData: ContactData = await request.json();

    // Walidacja danych
    if (!contactData.contact_companyName || !contactData.contact_companyName.trim()) {
      return NextResponse.json(
        { error: 'Nazwa firmy jest wymagana' },
        { status: 400 }
      );
    }

    if (!contactData.contact_contactPerson || !contactData.contact_contactPerson.trim()) {
      return NextResponse.json(
        { error: 'Osoba kontaktowa jest wymagana' },
        { status: 400 }
      );
    }

    if (!contactData.contact_email || !contactData.contact_email.trim()) {
      return NextResponse.json(
        { error: 'Email jest wymagany' },
        { status: 400 }
      );
    }

    if (!contactData.contact_phone || !contactData.contact_phone.trim()) {
      return NextResponse.json(
        { error: 'Telefon jest wymagany' },
        { status: 400 }
      );
    }

    if (!contactData.contact_category || !contactData.contact_category.trim()) {
      return NextResponse.json(
        { error: 'Kategoria jest wymagana' },
        { status: 400 }
      );
    }

    if (!contactData.contact_address || !contactData.contact_address.trim()) {
      return NextResponse.json(
        { error: 'Adres jest wymagany' },
        { status: 400 }
      );
    }

    await connectDB();

    // Utwórz nowy kontakt (userId = "0" dla kontaktów publicznych)
    const newContact = new Contact({
      contact_companyName: contactData.contact_companyName.trim(),
      contact_contactPerson: contactData.contact_contactPerson.trim(),
      contact_email: contactData.contact_email.trim(),
      contact_phone: contactData.contact_phone.trim(),
      contact_category: contactData.contact_category.trim(),
      contact_address: contactData.contact_address.trim(),
      contact_notes: contactData.contact_notes?.trim() || '',
      userId: "0" // Publiczne kontakty mają userId = "0"
    });

    const savedContact = await newContact.save();

    return NextResponse.json({
      success: true,
      message: 'Kontakt został pomyślnie dodany',
      contact: {
        id: savedContact._id,
        contact_companyName: savedContact.contact_companyName,
        contact_contactPerson: savedContact.contact_contactPerson,
        contact_email: savedContact.contact_email,
        contact_phone: savedContact.contact_phone,
        contact_category: savedContact.contact_category,
        contact_address: savedContact.contact_address,
        contact_notes: savedContact.contact_notes,
        createdAt: savedContact.createdAt,
        updatedAt: savedContact.updatedAt
      }
    });

  } catch (error) {
    console.error('Błąd dodawania kontaktu:', error);

    return NextResponse.json(
      { error: 'Wystąpił błąd podczas dodawania kontaktu' },
      { status: 500 }
    );
  }
}
