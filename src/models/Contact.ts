import mongoose, { Document, Schema } from 'mongoose';

export interface IContact extends Document {
  contact_companyName: string;
  contact_contactPerson: string;
  contact_email: string;
  contact_phone: string;
  contact_category: string;
  contact_address: string;
  contact_notes: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
}

const ContactSchema: Schema = new Schema({
  contact_companyName: {
    type: String,
    required: [true, 'Nazwa firmy jest wymagana'],
    trim: true,
    maxlength: [255, 'Nazwa firmy nie może być dłuższa niż 255 znaków']
  },
  contact_contactPerson: {
    type: String,
    required: [true, 'Osoba kontaktowa jest wymagana'],
    trim: true,
    maxlength: [255, 'Osoba kontaktowa nie może być dłuższa niż 255 znaków']
  },
  contact_email: {
    type: String,
    required: [true, 'Email jest wymagany'],
    trim: true,
    maxlength: [500, 'Email nie może by<PERSON> dłu<PERSON><PERSON>y niż 500 znaków']
  },
  contact_phone: {
    type: String,
    required: [true, 'Telefon jest wymagany'],
    trim: true,
    maxlength: [50, 'Telefon nie może być dłuższy niż 50 znaków']
  },
  contact_category: {
    type: String,
    required: [true, 'Kategoria jest wymagana'],
    trim: true,
    maxlength: [100, 'Kategoria nie może być dłuższa niż 100 znaków']
  },
  contact_address: {
    type: String,
    required: [true, 'Adres jest wymagany'],
    trim: true,
    maxlength: [500, 'Adres nie może być dłuższy niż 500 znaków']
  },
  contact_notes: {
    type: String,
    trim: true,
    maxlength: [1000, 'Notatki nie mogą być dłuższe niż 1000 znaków'],
    default: ''
  },
  userId: {
    type: String,
    required: [true, 'ID użytkownika jest wymagane']
  }
}, {
  timestamps: true
});

// Indeks dla szybszego wyszukiwania kontaktów użytkownika
ContactSchema.index({ userId: 1, createdAt: -1 });

// Indeks dla wyszukiwania po kategorii
ContactSchema.index({ userId: 1, contact_category: 1 });

// Indeks dla wyszukiwania po nazwie firmy
ContactSchema.index({ userId: 1, contact_companyName: 1 });

export default mongoose.models.Contact || mongoose.model<IContact>('Contact', ContactSchema);
