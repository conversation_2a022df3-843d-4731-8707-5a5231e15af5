RELACJE MIĘDZY ELEMENTAMI API - AI STUDIO

=== FUNKCJONALNOŚĆ LOKALIZACJI W WYSZUKIWANIU ===

OPIS:
Dodano obsługę lokalizacji w systemie wyszukiwania, umożliwiającą użytkownikom określenie geograficznej lokalizacji dla zapytań wyszukiwania Google przez SerpAPI.

ZMIANY W MODELACH DANYCH:

1. Model SearchQuery (src/models/SearchQuery.ts):
   - Dodano pole: location: String (optional, max 200 chars)
   - Zaktualizowano indeks: { userId: 1, query: 1, category: 1, location: 1 }
   - Dodano nowy indeks: { location: 1 }

2. Model Link (src/models/Link.ts):
   - Dodano pole: location: String (optional, max 200 chars)
   - Dodano nowy indeks: { location: 1 }

ZMIANY W API:

1. POST /api/search:
   - Dodano parametr: location (opcjonalny)
   - Przekazywanie parametru location do SerpAPI
   - Zapisywanie lokalizacji w SearchQuery i Link
   - Uwzględnienie lokalizacji przy sprawdzaniu istniejących zapytań

2. GET /api/links:
   - Dodano parametr: location (opcjonalny)
   - Filtrowanie linków po lokalizacji (case-insensitive regex)

ZMIANY W INTERFEJSIE:

1. Formularz wyszukiwania (/dashboard/agregator):
   - Dodano pole "Lokalizacja (opcjonalnie)"
   - Placeholder: "np. Warsaw, Poland"
   - Walidacja: max 200 znaków
   - Grid zmieniony na 3 kolumny (md:grid-cols-3)

2. Wyniki wyszukiwania:
   - Wyświetlanie lokalizacji w opisie wyników
   - Informacja o lokalizacji w komunikatach sukcesu

3. Lista linków (/dashboard/agregator/links):
   - Dodano pole filtra lokalizacji
   - Dodano kolumnę "Lokalizacja" w tabeli
   - Grid filtrów zmieniony na 5 kolumn (md:grid-cols-5)
   - Wyświetlanie "-" gdy brak lokalizacji

INTEGRACJA Z SERPAPI:
- Używa parametru 'location' zgodnie z dokumentacją SerpAPI
- Format: "City, Country" (np. "Warsaw, Poland")
- Parametr jest opcjonalny - gdy nie podano, wyszukiwanie globalne

PRZYPADKI UŻYCIA:
1. Wyszukiwanie globalne: pole lokalizacji puste
2. Wyszukiwanie lokalne: "Warsaw, Poland", "New York, USA"
3. Filtrowanie wyników: znajdowanie linków z konkretnej lokalizacji
4. Powtarzanie zapytań: uwzględnia lokalizację przy sprawdzaniu duplikatów

KOMPATYBILNOŚĆ WSTECZNA:
- Istniejące zapytania bez lokalizacji działają bez zmian
- Pole location jest opcjonalne we wszystkich modelach
- API zachowuje kompatybilność z istniejącymi klientami

DATA IMPLEMENTACJI: 2025-07-23

=== RĘCZNY WYBÓR STRONY GOOGLE ===

OPIS:
Dodano możliwość ręcznego wyboru strony Google, z której mają być pobrane wyniki.
Użytkownik może wybrać konkretną stronę (1-100) lub pozostawić pole puste dla automatycznej paginacji.

ZMIANY W INTERFEJSIE:

1. Formularz wyszukiwania (/dashboard/agregator):
   - Dodano pole "Strona (opcjonalnie)"
   - Type: number, min: 1, max: 100
   - Placeholder: "1"
   - Grid zmieniony na 4 kolumny (md:grid-cols-4)
   - Walidacja: 1-100 stron

2. Schema walidacji:
   ```typescript
   page: z
     .number()
     .min(1, "Strona musi być większa niż 0")
     .max(100, "Maksymalnie 100 stron")
     .optional()
   ```

ZMIANY W API:

1. POST /api/search:
   - Dodano parametr: page (opcjonalny)
   - Nowa logika wyboru strony:
     ```javascript
     if (page && page > 0) {
       pageNumber = page; // Ręczny wybór
     } else {
       // Automatyczna paginacja (istniejąca logika)
       const existingQuery = await SearchQuery.findOne({...}).sort({ page: -1 });
       pageNumber = existingQuery ? existingQuery.page + 1 : 1;
     }
     ```

LOGIKA DZIAŁANIA:

1. **Ręczny wybór strony:**
   - Użytkownik wpisuje numer strony (np. 5)
   - System pomija automatyczną paginację
   - Pobiera wyniki z wybranej strony
   - Zapisuje w bazie z wybranym numerem strony

2. **Automatyczna paginacja:**
   - Pole strony pozostaje puste
   - System sprawdza ostatnią stronę dla tej kombinacji query+category+location
   - Automatycznie zwiększa numer strony o 1

INTERFEJS UŻYTKOWNIKA:

1. Wyniki wyszukiwania:
   - Wyświetla "Strona: X (wybrana ręcznie)" dla ręcznego wyboru
   - Wyświetla "Strona: X" dla automatycznej paginacji

2. Komunikaty sukcesu:
   - "(wybrana strona X)" dla ręcznego wyboru
   - "(strona X)" dla automatycznej paginacji

3. Opis formularza:
   - "Możesz wybrać konkretną stronę lub pozostawić puste dla automatycznej paginacji"

PRZYPADKI UŻYCIA:

1. **Przeskoczenie do konkretnej strony:**
   - Użytkownik chce zobaczyć wyniki ze strony 10
   - Wpisuje "10" w pole strony
   - Otrzymuje wyniki ze strony 10

2. **Kontynuacja automatycznej paginacji:**
   - Użytkownik pozostawia pole strony puste
   - System automatycznie pobiera kolejną stronę

3. **Powrót do wcześniejszej strony:**
   - Użytkownik może ręcznie wybrać stronę 3, nawet jeśli wcześniej był na stronie 7

KOMPATYBILNOŚĆ:
- Zachowuje pełną kompatybilność z automatyczną paginacją
- Pole page jest opcjonalne we wszystkich miejscach
- Istniejące zapytania działają bez zmian

WALIDACJA:
- Minimum: 1 strona
- Maximum: 100 stron
- Type: number
- Opcjonalne pole

DATA IMPLEMENTACJI: 2025-07-23

=== ZMIANA UNIKALNOŚCI DOMEN ===

OPIS PROBLEMU:
Wcześniej domeny mogły się powtarzać w różnych kategoriach dla tego samego użytkownika.
Przykład: panoramafirm.pl mogła istnieć w kategorii "budowlanka" i "Trenerzy".

ROZWIĄZANIE:
Zmieniono unikalność domen z per-kategoria na globalną dla użytkownika.
Teraz każda domena może istnieć tylko raz dla danego użytkownika, niezależnie od kategorii.

ZMIANY W BAZIE DANYCH:
- Stary indeks: { userId: 1, fullDomain: 1, category: 1 } (unique)
- Nowy indeks: { userId: 1, fullDomain: 1 } (unique)

NOWE API ENDPOINTS:
- GET /api/domains/check-duplicates - sprawdza duplikaty domen
- POST /api/domains/check-duplicates - usuwa duplikaty według strategii

STRATEGIE USUWANIA DUPLIKATÓW:
- keep-oldest: zachowuje najstarszą domenę
- keep-newest: zachowuje najnowszą domenę
- keep-highest-links: zachowuje domenę z największą liczbą linków

WYKONANA MIGRACJA:
✅ 1. Sprawdzono duplikaty: znaleziono 119 grup duplikatów (258 domen)
✅ 2. Usunięto duplikaty: strategia "keep-highest-links" - usunięto 139 domen
✅ 3. Zmieniono indeks unikalności: user_domain_unique_index
✅ 4. Zaktualizowano API endpoints: manual, search, create-from-links
✅ 5. Przetestowano unikalność: działa poprawnie

PRZYKŁAD ROZWIĄZANIA PROBLEMU:
- panoramafirm.pl istniała w 5 kategoriach (Budowlanka, Fotowoltaika, Księgowość, Prawo, Trenerzy)
- Zachowano domenę z kategorii "Budowlanka" (2 linki, status: rejected)
- Usunięto 4 duplikaty z pozostałych kategorii

SKRYPTY POMOCNICZE:
- scripts/check-duplicates-direct.js - sprawdza duplikaty bezpośrednio w bazie
- scripts/remove-duplicates.js - usuwa duplikaty według strategii
- scripts/migrate-domain-uniqueness.js - migruje indeksy unikalności
- scripts/test-uniqueness.js - testuje działanie unikalności

=== ENDPOINT: /api/domains/to-audit ===

OPIS:
Endpoint zwracający domeny WordPress ze statusem 'new' lub 'in_audit' przeznaczone do audytu bezpieczeństwa.

PARAMETRY GET:
- limit (opcjonalny): Number - ogranicza liczbę zwracanych domen
- category (opcjonalny): String - filtruje domeny po kategorii (case-insensitive)
- id (opcjonalny): String - zwraca tylko domenę o podanym MongoDB ObjectId

LOGIKA FILTROWANIA:
1. Bazowe filtry (zawsze aktywne):
   - status: 'new' lub 'in_audit'
   - cms: 'wordpress'

2. Filtry opcjonalne:
   - Jeśli podano 'category': dodaje filtr regex case-insensitive
   - Jeśli podano 'id': dodaje filtr po _id (ma priorytet w komunikacie)

3. Limit:
   - Jeśli podano 'limit' i nie ma 'id': ogranicza wyniki
   - Jeśli podano 'id': limit jest ignorowany (max 1 wynik)

FORMAT ODPOWIEDZI:
{
  "success": true,
  "domains": [
    {
      "id": "MongoDB ObjectId",
      "domain": "example.com",
      "protocol": "https",
      "fullDomain": "https://example.com",
      "category": "Test",
      "cms": "wordpress",
      "status": "new", // lub "in_audit"
      "linkCount": 1,
      "metadata": {}
    }
  ],
  "count": 1,
  "totalCount": 1,
  "message": "Komunikat w języku polskim"
}

KOMUNIKATY:
- Z ID (znaleziono): "Znaleziono domenę WordPress o ID "{id}" do audytu bezpieczeństwa"
- Z ID (nie znaleziono): "Nie znaleziono domeny WordPress o ID "{id}" do audytu bezpieczeństwa"
- Bez ID (brak wyników): "Brak domen WordPress do audytu bezpieczeństwa [w kategorii "{category}"]"
- Bez ID (z limitem): "Znaleziono {count} z {totalCount} domen WordPress do audytu bezpieczeństwa [w kategorii "{category}"]"
- Bez ID (bez limitu): "Znaleziono {count} domen WordPress do audytu bezpieczeństwa [w kategorii "{category}"]"

RELACJE Z INNYMI ELEMENTAMI:
- Model: Domain (src/models/Domain.ts)
- Baza danych: MongoDB przez connectDB (src/lib/db.ts)
- Używane przez: skrypt audytu (scan/wpprobe_audit_script.sh)
- Powiązane endpointy:
  - POST /api/domains/[id]/audit - zapisuje wyniki audytu
  - GET /api/domains/[id]/audit-content - pobiera treść audytu (chroniony)

PRZYPADKI UŻYCIA:
1. Pobieranie listy domen do audytu: GET /api/domains/to-audit?limit=10&category=production
2. Sprawdzenie konkretnej domeny: GET /api/domains/to-audit?id=686fe64813ce75e6a2f1de3f
3. Pobieranie wszystkich domen z kategorii: GET /api/domains/to-audit?category=test

BEZPIECZEŃSTWO:
- Endpoint publiczny (bez autoryzacji)
- Nie zwraca treści audytu (auditContent)
- Nie zwraca danych lighthouse (lighthouseContent)
- Filtruje tylko domeny WordPress ze statusem 'new' lub 'in_audit'

ZMIANY (2025-07-23):
- Dodano obsługę parametru 'id' dla filtrowania po konkretnym ID domeny
- Zaktualizowano logikę komunikatów dla wyszukiwania po ID
- Zachowano kompatybilność wsteczną z istniejącymi parametrami
- ZMIANA: Rozszerzono filtrowanie o status 'in_audit' - endpoint zwraca teraz domeny ze statusem 'new' lub 'in_audit'

=== ENDPOINT: /api/domains/[id]/execute-audit ===

OPIS:
Endpoint do wykonywania audytu domeny - wysyła dane domeny na zewnętrzny webhook.

METODA: POST
AUTORYZACJA: Wymagana (sesja użytkownika)

PARAMETRY:
- id (w URL): String - MongoDB ObjectId domeny

LOGIKA:
1. Sprawdza autoryzację użytkownika
2. Weryfikuje czy domena istnieje i należy do użytkownika
3. Sprawdza czy domena nie ma już audytu (auditContent)
4. Wysyła dane domeny na webhook (AUDIT_WEBHOOK_URL)
5. Aktualizuje status domeny na 'in_audit'
6. Ustawia metadata.auditRequested = true

DANE WYSYŁANE NA WEBHOOK:
{
  "domainId": "MongoDB ObjectId",
  "domain": "example.com",
  "fullDomain": "https://example.com",
  "protocol": "https",
  "category": "Test",
  "userId": "user_id",
  "requestedAt": "2025-07-23T12:00:00.000Z"
}

FORMAT ODPOWIEDZI SUKCES:
{
  "success": true,
  "message": "Audit request sent successfully",
  "data": {
    "domainId": "MongoDB ObjectId",
    "domain": "example.com",
    "status": "in_audit"
  }
}

BŁĘDY:
- 401: Brak autoryzacji
- 404: Domena nie znaleziona lub brak dostępu
- 400: Domena już ma audyt
- 500: Błąd webhook lub konfiguracji

RELACJE Z INNYMI ELEMENTAMI:
- Model: Domain (src/models/Domain.ts)
- Zmienna środowiskowa: AUDIT_WEBHOOK_URL (.env)
- Frontend: src/app/dashboard/domains/[id]/page.tsx (przycisk "Wykonaj audyt")
- Powiązane endpointy:
  - GET /api/domains/[id] - pobiera szczegóły domeny (sprawdza secureAudit)
  - POST /api/domains/[id]/audit - zapisuje wyniki audytu po wykonaniu
  - GET /api/dashboard/audit-stats - wykorzystuje metadata.dateAudit do statystyk

AKTUALIZACJA LICZBY AUDYTÓW:
- Po kliknięciu "Wykonaj audyt" natychmiast zwiększa liczbę audytów
- Ustawia metadata.hasAudit: true i metadata.dateAudit: dzisiejsza data
- Dzięki temu audyt jest od razu liczony w statystykach (wykres audytów)
- Endpoint /api/domains/[id]/audit zachowuje oryginalną datę audytu (nie nadpisuje dateAudit)

ENDPOINT TO-AUDIT:
- GET /api/domains/to-audit automatycznie dodaje metadata audytu przy pierwszym pobraniu
- Ustawia te same pola co execute-audit: auditRequested, auditRequestedAt, hasAudit, dateAudit
- Domeny pobrane z zewnątrz są od razu liczone w statystykach audytów
- Filtruje domeny które nie mają jeszcze ustawionych pól audytu i je aktualizuje

PRZYPADKI UŻYCIA:
1. Użytkownik klika "Wykonaj audyt" w interfejsie domeny
2. System wysyła żądanie audytu na zewnętrzny webhook
3. Zewnętrzny system wykonuje audyt i zapisuje wyniki przez POST /api/domains/[id]/audit

BEZPIECZEŃSTWO:
- Wymaga autoryzacji użytkownika
- Sprawdza własność domeny
- Waliduje czy domena nie ma już audytu
- Obsługuje błędy webhook

=== FUNKCJONALNOŚĆ: OBSŁUGA WIELU ADRESÓW EMAIL ===

OPIS:
System obsługuje przypadki, gdy backend zwraca string z wieloma adresami email oddzielonymi przecinkami.
W formularzach wysyłki maili automatycznie używany jest tylko pierwszy adres email.

IMPLEMENTACJA:
- Lokalizacja: src/app/dashboard/domains/[id]/audit/page.tsx
- Funkcja pomocnicza: getFirstEmail(emailString: string)
- Logika: emailString.split(',')[0].trim()

ZASTOSOWANIE:
1. Pre-populacja pola email z danych kontaktowych:
   - Pobiera contact_email z backend
   - Automatycznie ekstraktuje pierwszy email jeśli zawiera przecinki
   - Wyświetla tylko pierwszy email w polu formularza

2. Wysyłanie emaili:
   - Przed wysłaniem ekstraktuje pierwszy email z pola
   - Używa tylko pierwszego adresu do wysyłki
   - Pokazuje dialog potwierdzenia z informacją o używanym adresie

3. Komunikaty użytkownika:
   - Ostrzeżenie w dialogu: "(Zostanie użyty pierwszy z podanych adresów)"
   - Toast sukcesu: "Email został wysłany na {firstEmail} (pierwszy z podanych adresów)"

=== EMAIL TRACKING - DEDUPLIKACJA OTWARĆ ===

PROBLEM:
- System zapisywał event 'opened' za każdym razem gdy użytkownik otworzył email
- Powodowało to nieprawidłowe statystyki (wielokrotne otwarcia tego samego emaila)

ROZWIĄZANIE:
- Dodano sprawdzanie czy event 'opened' już istnieje przed zapisaniem nowego
- Tylko pierwsze otwarcie emaila jest zapisywane jako event

IMPLEMENTACJA:
1. src/lib/mail-tracking.ts - funkcja handleEmailEvent():
   - Sprawdza czy istnieje już event 'opened' dla danego emailHistoryId i recipientEmail
   - Jeśli istnieje, pomija tworzenie nowego eventu
   - Loguje informację o pominięciu duplikatu

2. src/app/api/mail-tracking/[...path]/route.ts - obsługa tracking pixel:
   - Sprawdza czy istnieje już event 'opened' przed zapisaniem
   - Używa tego samego mechanizmu co handleEmailEvent()
   - Zapewnia spójność niezależnie od ścieżki trackingu

LOGIKA SPRAWDZANIA:
```javascript
const existingOpenEvent = await EmailTracking.findOne({
  emailHistoryId: emailHistory._id,
  recipientEmail: decoded.recipient,
  eventType: 'opened'
});

if (existingOpenEvent) {
  console.log(`Email already opened by ${decoded.recipient}, skipping duplicate event`);
  return; // lub continue bez zapisywania
}
```

WPŁYW NA STATYSTYKI:
- Open rate będzie teraz pokazywał rzeczywistą liczbę unikalnych otwarć
- Eliminuje zawyżone statystyki spowodowane wielokrotnym otwieraniem tego samego emaila
- Zachowuje dokładność analityki email marketingu

RELACJE Z INNYMI ELEMENTAMI:
- Model: Domain.contact_data.contact_email (może zawierać wiele emaili)
- API: /api/domains/[id]/contact (zwraca dane kontaktowe)
- API: /api/domains/[id]/send-audit-email (przyjmuje pojedynczy email)
- Frontend: formularz wysyłki emaili w audycie domeny

PRZYPADKI UŻYCIA:
1. Backend zwraca: "<EMAIL>,<EMAIL>"
   - Formularz pokazuje: "<EMAIL>"
   - Email wysyłany na: "<EMAIL>"

2. Backend zwraca: "<EMAIL>"
   - Formularz pokazuje: "<EMAIL>"
   - Email wysyłany na: "<EMAIL>" (bez zmian)

BEZPIECZEŃSTWO:
- Walidacja formatu email na poziomie API
- Trim() usuwa białe znaki z ekstraktowanego emaila
- Zachowana kompatybilność z pojedynczymi adresami email
